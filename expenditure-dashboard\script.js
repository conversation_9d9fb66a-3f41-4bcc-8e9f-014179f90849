// Dashboard Data
const dashboardData = {
    aiTransformation: {
        directProcessing: 16000,
        platformInfrastructure: 4200,
        humanResources: 13080,
        total: 33280
    },
    manualProcess: {
        laborCosts: 300000,
        errorCorrection: 40000,
        total: 340000
    },
    savings: {
        annual: 306720,
        perOrder: 76.68,
        percentage: 90.2
    },
    roi: {
        initialInvestment: 30000,
        monthlySavings: 25560,
        paybackMonths: 1.2,
        yearOneROI: 922
    }
};

// Chart configurations
const chartColors = {
    primary: '#667eea',
    secondary: '#764ba2',
    success: '#38a169',
    warning: '#ed8936',
    info: '#3182ce',
    light: '#f7fafc',
    dark: '#2d3748',
    gradient: ['#667eea', '#764ba2', '#38a169', '#ed8936', '#3182ce']
};

// Chart.js default colors for dark theme
Chart.defaults.color = '#e2e8f0';
Chart.defaults.borderColor = '#4a5568';
Chart.defaults.backgroundColor = '#2d3748';

// Initialize Dashboard
document.addEventListener('DOMContentLoaded', function () {
    initializeToggleButtons();
    initializeCharts();
    animateCounters();
});

// Toggle Button Functionality
function initializeToggleButtons() {
    const toggleButtons = document.querySelectorAll('.toggle-btn');
    const breakdownViews = document.querySelectorAll('.breakdown-view');

    toggleButtons.forEach(button => {
        button.addEventListener('click', function () {
            const viewType = this.getAttribute('data-view');

            // Update active button
            toggleButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Update active view
            breakdownViews.forEach(view => view.classList.remove('active'));
            document.getElementById(`${viewType}-view`).classList.add('active');

            // Update charts based on view
            updateChartsForView(viewType);
        });
    });
}

// Initialize All Charts
function initializeCharts() {
    createAICostChart();
    createManualCostChart();
    createComparisonChart();
    createROIChart();
    createScalabilityChart();
}

// AI Cost Breakdown Chart
function createAICostChart() {
    const ctx = document.getElementById('aiCostChart').getContext('2d');

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Direct Processing', 'Platform & Infrastructure', 'Human Resources'],
            datasets: [{
                data: [16000, 4200, 13080],
                backgroundColor: [chartColors.primary, chartColors.secondary, chartColors.success],
                borderWidth: 0,
                hoverOffset: 10
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        font: {
                            size: 12,
                            weight: '500'
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function (context) {
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${context.label}: $${value.toLocaleString()} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

// Manual Cost Breakdown Chart
function createManualCostChart() {
    const ctx = document.getElementById('manualCostChart').getContext('2d');

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Labor Costs', 'Error Correction'],
            datasets: [{
                data: [300000, 40000],
                backgroundColor: [chartColors.warning, '#e53e3e'],
                borderWidth: 0,
                hoverOffset: 10
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        font: {
                            size: 12,
                            weight: '500'
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function (context) {
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${context.label}: $${value.toLocaleString()} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

// Comparison Chart
function createComparisonChart() {
    const ctx = document.getElementById('comparisonChart').getContext('2d');

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['Annual Cost', 'Cost per Order', 'Processing Time (hours)', 'Accuracy Rate (%)'],
            datasets: [
                {
                    label: 'Manual Process',
                    data: [340000, 85, 3, 85],
                    backgroundColor: chartColors.warning,
                    borderRadius: 8
                },
                {
                    label: 'AI Transformation',
                    data: [33280, 8.32, 0.17, 99.2],
                    backgroundColor: chartColors.success,
                    borderRadius: 8
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        font: {
                            size: 12,
                            weight: '500'
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function (context) {
                            const label = context.dataset.label;
                            const value = context.parsed.y;
                            const dataIndex = context.dataIndex;

                            if (dataIndex === 0) return `${label}: $${value.toLocaleString()}`;
                            if (dataIndex === 1) return `${label}: $${value}`;
                            if (dataIndex === 2) return `${label}: ${value} hours`;
                            if (dataIndex === 3) return `${label}: ${value}%`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: '#4a5568'
                    },
                    ticks: {
                        color: '#a0aec0'
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#a0aec0'
                    }
                }
            }
        }
    });
}

// ROI Chart
function createROIChart() {
    const ctx = document.getElementById('roiChart').getContext('2d');

    const months = ['Month 1', 'Month 2', 'Month 3', 'Month 6', 'Month 12', 'Month 24'];
    const cumulativeSavings = [25560, 51120, 76680, 153360, 306720, 613440];
    const investment = [30000, 30000, 30000, 30000, 30000, 30000];

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: months,
            datasets: [
                {
                    label: 'Cumulative Savings',
                    data: cumulativeSavings,
                    borderColor: chartColors.success,
                    backgroundColor: chartColors.success + '20',
                    fill: true,
                    tension: 0.4,
                    pointRadius: 6,
                    pointHoverRadius: 8
                },
                {
                    label: 'Initial Investment',
                    data: investment,
                    borderColor: chartColors.warning,
                    backgroundColor: chartColors.warning + '20',
                    fill: false,
                    borderDash: [5, 5],
                    pointRadius: 4,
                    pointHoverRadius: 6
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        font: {
                            size: 12,
                            weight: '500'
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function (context) {
                            return `${context.dataset.label}: $${context.parsed.y.toLocaleString()}`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function (value) {
                            return '$' + value.toLocaleString();
                        },
                        color: '#a0aec0'
                    },
                    grid: {
                        color: '#4a5568'
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#a0aec0'
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
}

// Scalability Chart
function createScalabilityChart() {
    const ctx = document.getElementById('scalabilityChart').getContext('2d');

    const orderVolumes = [1000, 2000, 4000, 8000, 16000];
    const manualCosts = [85000, 170000, 340000, 680000, 1360000];
    const aiCosts = [21280, 25280, 33280, 49280, 81280];

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: orderVolumes,
            datasets: [
                {
                    label: 'Manual Process Cost',
                    data: manualCosts,
                    borderColor: chartColors.warning,
                    backgroundColor: chartColors.warning + '20',
                    fill: false,
                    tension: 0.1,
                    pointRadius: 6,
                    pointHoverRadius: 8
                },
                {
                    label: 'AI Transformation Cost',
                    data: aiCosts,
                    borderColor: chartColors.success,
                    backgroundColor: chartColors.success + '20',
                    fill: false,
                    tension: 0.1,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        font: {
                            size: 12,
                            weight: '500'
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function (context) {
                            return `${context.dataset.label}: $${context.parsed.y.toLocaleString()}`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function (value) {
                            return '$' + (value / 1000) + 'K';
                        },
                        color: '#a0aec0'
                    },
                    grid: {
                        color: '#4a5568'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Annual Order Volume',
                        color: '#a0aec0'
                    },
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#a0aec0'
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
}

// Update charts based on view
function updateChartsForView(viewType) {
    // This function can be expanded to update charts dynamically
    // based on the selected view
    console.log(`Updating charts for view: ${viewType}`);
}

// Animate counters
function animateCounters() {
    const counters = document.querySelectorAll('.card-value, .metric-value, .impact-value');

    counters.forEach(counter => {
        const target = parseFloat(counter.textContent.replace(/[$,K%]/g, ''));
        const duration = 2000;
        const increment = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }

            // Format the number based on original content
            const originalText = counter.textContent;
            if (originalText.includes('$')) {
                if (originalText.includes('K')) {
                    counter.textContent = '$' + (current / 1000).toFixed(1) + 'K';
                } else if (originalText.includes('M')) {
                    counter.textContent = '$' + (current / 1000000).toFixed(1) + 'M';
                } else {
                    counter.textContent = '$' + Math.round(current).toLocaleString();
                }
            } else if (originalText.includes('%')) {
                counter.textContent = Math.round(current) + '%';
            } else if (originalText.includes('months')) {
                counter.textContent = current.toFixed(1) + ' months';
            } else {
                counter.textContent = Math.round(current).toLocaleString();
            }
        }, 16);
    });
}

// Utility function to format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
}

// Utility function to format percentage
function formatPercentage(value) {
    return value.toFixed(1) + '%';
}
