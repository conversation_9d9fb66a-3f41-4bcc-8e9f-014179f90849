// SWIFT ROI Dashboard JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize animations and charts
    initializeAnimations();
    createROIChart();
    addInteractiveElements();
});

function initializeAnimations() {
    // Animate metric values on load
    const metricValues = document.querySelectorAll('.metric-value');
    
    metricValues.forEach((element, index) => {
        setTimeout(() => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';
            element.style.transition = 'all 0.6s ease';
            
            setTimeout(() => {
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
                animateNumber(element);
            }, 100);
        }, index * 200);
    });

    // Animate cards on scroll
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    });

    document.querySelectorAll('.card, .breakdown-card, .risk-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'all 0.6s ease';
        observer.observe(card);
    });
}

function animateNumber(element) {
    const text = element.textContent;
    const hasPercent = text.includes('%');
    const hasDollar = text.includes('$');
    const hasM = text.includes('M');
    const hasK = text.includes('K');
    
    // Extract number from text
    let number = parseFloat(text.replace(/[^0-9.]/g, ''));
    
    if (isNaN(number)) return;
    
    let current = 0;
    const increment = number / 50;
    const timer = setInterval(() => {
        current += increment;
        if (current >= number) {
            current = number;
            clearInterval(timer);
        }
        
        let displayValue = Math.floor(current);
        if (number < 10 && number % 1 !== 0) {
            displayValue = current.toFixed(1);
        }
        
        let formattedValue = displayValue;
        if (hasDollar) formattedValue = '$' + formattedValue;
        if (hasM) formattedValue += 'M';
        if (hasK) formattedValue += 'K';
        if (hasPercent) formattedValue += '%';
        
        element.textContent = formattedValue;
    }, 20);
}

function createROIChart() {
    const ctx = document.getElementById('roiChart').getContext('2d');
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['Year 1', 'Year 2', 'Year 3'],
            datasets: [{
                label: 'Investment',
                data: [1000000, 100000, 100000],
                backgroundColor: 'rgba(231, 76, 60, 0.8)',
                borderColor: 'rgba(231, 76, 60, 1)',
                borderWidth: 2
            }, {
                label: 'Benefits',
                data: [6200000, 6200000, 6200000],
                backgroundColor: 'rgba(39, 174, 96, 0.8)',
                borderColor: 'rgba(39, 174, 96, 1)',
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '3-Year Investment vs Benefits',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + (value / 1000000).toFixed(1) + 'M';
                        }
                    }
                }
            },
            animation: {
                duration: 2000,
                easing: 'easeInOutQuart'
            }
        }
    });
}

function addInteractiveElements() {
    // Add hover effects to improvement badges
    const improvementBadges = document.querySelectorAll('.improvement-badge');
    improvementBadges.forEach(badge => {
        badge.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.1)';
            this.style.transition = 'transform 0.2s ease';
        });
        
        badge.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });

    // Add click-to-highlight functionality for breakdown cards
    const breakdownCards = document.querySelectorAll('.breakdown-card');
    breakdownCards.forEach(card => {
        card.addEventListener('click', function() {
            // Remove highlight from other cards
            breakdownCards.forEach(c => c.classList.remove('active'));
            
            // Add highlight to clicked card
            this.classList.add('active');
            
            // Show detailed breakdown
            showDetailedBreakdown(this);
        });
    });

    // Add tooltip functionality
    addTooltips();
}

function showDetailedBreakdown(card) {
    const cardTitle = card.querySelector('h3').textContent;
    const cardAmount = card.querySelector('.breakdown-amount').textContent;
    
    // Create modal or detailed view
    const modal = document.createElement('div');
    modal.className = 'breakdown-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <h3>${cardTitle}</h3>
            <div class="modal-amount">${cardAmount}</div>
            <div class="detailed-calculation">
                ${getDetailedCalculation(cardTitle)}
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Close modal functionality
    modal.querySelector('.close-modal').addEventListener('click', () => {
        document.body.removeChild(modal);
    });
    
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });
}

function getDetailedCalculation(title) {
    const calculations = {
        'Labor Cost Savings': `
            <p><strong>Calculation Details:</strong></p>
            <ul>
                <li>Current FTEs: 6 @ $75,000/year = $450,000</li>
                <li>SWIFT FTEs: 4 @ $75,000/year = $300,000</li>
                <li>Reduction: 2 FTEs = $150,000 annual savings</li>
                <li>33% workforce reduction in O2C processing</li>
            </ul>
        `,
        'Error Reduction Savings': `
            <p><strong>Calculation Details:</strong></p>
            <ul>
                <li>Current error rate: 3%</li>
                <li>SWIFT error rate: 1.5%</li>
                <li>Error reduction: 1.5%</li>
                <li>Annual volume: $75M</li>
                <li>Error cost impact: 2% of error volume</li>
                <li>Savings: 1.5% × $75M × 2% = $22,500</li>
            </ul>
        `,
        'Revenue Uplift': `
            <p><strong>Calculation Details:</strong></p>
            <ul>
                <li>AI-driven recommendations increase sales</li>
                <li>Cross-sell and upsell opportunities</li>
                <li>Improved customer experience</li>
                <li>5% uplift × $75M annual volume = $3,750,000</li>
                <li>Conservative estimate based on client data</li>
            </ul>
        `,
        'Process Efficiency Gains': `
            <p><strong>Calculation Details:</strong></p>
            <ul>
                <li>Faster order processing (120min → 3min)</li>
                <li>Improved customer satisfaction</li>
                <li>Reduced cycle times</li>
                <li>Enhanced cash flow</li>
                <li>Estimated 3% of annual volume = $2,250,000</li>
            </ul>
        `
    };
    
    return calculations[title] || '<p>Detailed calculation not available.</p>';
}

function addTooltips() {
    // Add CSS for tooltips
    const style = document.createElement('style');
    style.textContent = `
        .breakdown-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }
        
        .close-modal {
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }
        
        .close-modal:hover {
            color: #333;
        }
        
        .modal-amount {
            font-size: 2rem;
            font-weight: bold;
            color: #27ae60;
            margin: 15px 0;
        }
        
        .detailed-calculation ul {
            margin: 15px 0;
            padding-left: 20px;
        }
        
        .detailed-calculation li {
            margin: 8px 0;
            line-height: 1.4;
        }
        
        .breakdown-card.active {
            transform: scale(1.05);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }
    `;
    document.head.appendChild(style);
}

// Add smooth scrolling for better UX
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        document.querySelector(this.getAttribute('href')).scrollIntoView({
            behavior: 'smooth'
        });
    });
});

// Add keyboard navigation
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const modal = document.querySelector('.breakdown-modal');
        if (modal) {
            document.body.removeChild(modal);
        }
    }
});
