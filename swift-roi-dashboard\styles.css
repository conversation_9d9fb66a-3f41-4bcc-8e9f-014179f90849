/* SWIFT ROI Dashboard Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.dashboard-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.dashboard-header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.dashboard-header h1 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 10px;
}

.dashboard-header h1 i {
    color: #3498db;
    margin-right: 15px;
}

.subtitle {
    font-size: 1.1rem;
    color: #7f8c8d;
    font-weight: 300;
}

/* Summary Cards */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.highlight-card {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
}

.card-icon {
    font-size: 2.5rem;
    margin-right: 20px;
    color: #3498db;
}

.highlight-card .card-icon {
    color: rgba(255, 255, 255, 0.9);
}

.card-content h3 {
    font-size: 1rem;
    margin-bottom: 8px;
    opacity: 0.8;
}

.metric-value {
    font-size: 2.2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.metric-subtitle {
    font-size: 0.85rem;
    opacity: 0.7;
}

/* Comparison Section */
.comparison-section {
    margin-bottom: 40px;
}

.comparison-section h2 {
    color: white;
    font-size: 1.8rem;
    margin-bottom: 25px;
    text-align: center;
}

.comparison-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.comparison-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.swift-improvements {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
}

.comparison-card h3 {
    font-size: 1.4rem;
    margin-bottom: 20px;
    text-align: center;
}

.metric-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.swift-improvements .metric-row {
    border-bottom-color: rgba(255, 255, 255, 0.2);
}

.metric-label {
    font-weight: 500;
}

.improvement-badge {
    background: rgba(46, 204, 113, 0.2);
    color: #27ae60;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    margin-left: 10px;
}

.swift-improvements .improvement-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* Financial Breakdown */
.financial-breakdown {
    margin-bottom: 40px;
}

.financial-breakdown h2 {
    color: white;
    font-size: 1.8rem;
    margin-bottom: 25px;
    text-align: center;
}

.breakdown-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.breakdown-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.breakdown-card:hover {
    transform: translateY(-3px);
}

.breakdown-card.highlight {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.breakdown-header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
}

.breakdown-header i {
    font-size: 1.5rem;
    margin-right: 10px;
    color: #3498db;
}

.breakdown-card.highlight .breakdown-header i {
    color: rgba(255, 255, 255, 0.9);
}

.breakdown-amount {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.breakdown-detail {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Investment Analysis */
.investment-analysis {
    margin-bottom: 40px;
}

.investment-analysis h2 {
    color: white;
    font-size: 1.8rem;
    margin-bottom: 25px;
    text-align: center;
}

.investment-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    align-items: center;
}

.investment-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.investment-card h3 {
    font-size: 1.4rem;
    margin-bottom: 20px;
    text-align: center;
    color: #2c3e50;
}

.investment-row {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.investment-row:last-child {
    border-bottom: none;
}

.year {
    font-weight: bold;
    width: 80px;
}

.amount {
    font-size: 1.2rem;
    font-weight: bold;
    color: #e74c3c;
    width: 120px;
}

.description {
    font-size: 0.9rem;
    color: #7f8c8d;
}

.roi-visualization {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Risk Analysis */
.risk-analysis {
    margin-bottom: 40px;
}

.risk-analysis h2 {
    color: white;
    font-size: 1.8rem;
    margin-bottom: 25px;
    text-align: center;
}

.risk-cards {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.risk-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.risk-card.conservative {
    border-left: 5px solid #f39c12;
}

.risk-card.realistic {
    border-left: 5px solid #27ae60;
}

.risk-card h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
    color: #2c3e50;
}

.scenario-desc {
    color: #7f8c8d;
    margin-bottom: 15px;
}

.scenario-benefit {
    font-size: 1.8rem;
    font-weight: bold;
    color: #27ae60;
    margin-bottom: 10px;
}

.scenario-roi {
    font-size: 1.2rem;
    font-weight: bold;
    color: #3498db;
}

/* Insights Section */
.insights-section {
    margin-bottom: 40px;
}

.insights-section h2 {
    color: white;
    font-size: 1.8rem;
    margin-bottom: 25px;
    text-align: center;
}

.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.insight-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
}

.insight-card i {
    font-size: 1.5rem;
    color: #27ae60;
    margin-right: 15px;
}

.insight-card p {
    font-size: 0.95rem;
    line-height: 1.4;
}

/* Footer */
.dashboard-footer {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    color: white;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .comparison-grid,
    .investment-grid,
    .risk-cards {
        grid-template-columns: 1fr;
    }
    
    .dashboard-header h1 {
        font-size: 2rem;
    }
    
    .metric-value {
        font-size: 1.8rem;
    }
    
    .breakdown-amount {
        font-size: 1.6rem;
    }
}
