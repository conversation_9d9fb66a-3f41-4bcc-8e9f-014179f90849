#!/usr/bin/env python3
"""
Demo O2C CLI - Demonstrates the O2C workflow with sample data
Uses sample_po.txt and database information to show realistic results
"""

import os
import sys
import json
import time
import random
from datetime import datetime, timedelta
from pathlib import Path

class DemoO2CCLI:
    """Demo O2C CLI that demonstrates workflow with sample data"""
    
    def __init__(self):
        """Initialize demo CLI interface"""
        # CLI styling
        self.HEADER = '\033[95m'
        self.BLUE = '\033[94m'
        self.CYAN = '\033[96m'
        self.GREEN = '\033[92m'
        self.YELLOW = '\033[93m'
        self.RED = '\033[91m'
        self.BOLD = '\033[1m'
        self.END = '\033[0m'

        # Track execution times
        self.stage_times = []
        self.total_start_time = None
        
        # Sample data from the database and PO
        self.sample_po_data = {
            "po_number": "PO-VALID-2025-002",
            "customer": {
                "name": "ABC Manufacturing Corp",
                "customer_id": "CUST-001",
                "email": "<EMAIL>",
                "phone": "(*************"
            },
            "line_items": [
                {
                    "product_code": "WIDGET-A100",
                    "description": "Premium Widget Assembly Type A",
                    "quantity": 25,
                    "unit_price": 125.00,
                    "line_total": 3125.00
                },
                {
                    "product_code": "BOLT-M8-50", 
                    "description": "M8 x 50mm Stainless Steel Bolts",
                    "quantity": 100,
                    "unit_price": 2.50,
                    "line_total": 250.00
                }
            ],
            "pricing": {
                "subtotal": 3375.00,
                "tax": 278.44,
                "shipping": 75.00,
                "total": 3728.44
            },
            "delivery_date": "2025-08-10",
            "payment_terms": "Net 30 Days"
        }
        
        # Database-based sample data
        self.customer_data = {
            "CUST-001": {
                "name": "ABC Manufacturing Corp",
                "status": "ACTIVE",
                "credit_limit": 50000.0,
                "current_balance": 12500.0,
                "available_credit": 37500.0,
                "risk_score": "LOW",
                "credit_rating": "A-",
                "payment_terms": "Net 30"
            }
        }
        
        self.inventory_data = {
            "WIDGET-A100": {
                "quantity_on_hand": 500,
                "quantity_available": 425,
                "quantity_allocated": 75,
                "warehouse_location": "MAIN",
                "lead_time_days": 7
            },
            "BOLT-M8-50": {
                "quantity_on_hand": 2000,
                "quantity_available": 1800,
                "quantity_allocated": 200,
                "warehouse_location": "MAIN", 
                "lead_time_days": 3
            }
        }
        
    def print_header(self, text: str):
        """Print styled header"""
        print(f"\n{self.HEADER}{self.BOLD}{'='*80}{self.END}")
        print(f"{self.HEADER}{self.BOLD}{text.center(80)}{self.END}")
        print(f"{self.HEADER}{self.BOLD}{'='*80}{self.END}\n")
        
    def print_stage(self, stage_num: int, stage_name: str):
        """Print stage header"""
        print(f"\n{self.GREEN}{self.BOLD}{'='*80}{self.END}")
        print(f"{self.GREEN}{self.BOLD}STAGE {stage_num}: {stage_name.upper()}{self.END}".center(80))
        print(f"{self.GREEN}{self.BOLD}{'='*80}{self.END}")
        
    def print_success(self, text: str):
        """Print success message"""
        print(f"{self.GREEN}✓ {text}{self.END}")
        
    def print_warning(self, text: str):
        """Print warning message"""
        print(f"{self.YELLOW}⚠ {text}{self.END}")
        
    def print_error(self, text: str):
        """Print error message"""
        print(f"{self.RED}✗ {text}{self.END}")
        
    def print_info(self, text: str):
        """Print info message"""
        print(f"{self.BLUE}ℹ {text}{self.END}")
        
    def get_user_approval(self, prompt: str = "Continue?") -> bool:
        """Get user approval to proceed"""
        while True:
            response = input(f"\n{self.YELLOW}► {prompt} (y/n/q): {self.END}").lower().strip()
            if response in ['y', 'yes']:
                return True
            elif response in ['n', 'no']:
                return False
            elif response in ['q', 'quit']:
                print(f"\n{self.RED}Workflow terminated by user.{self.END}")
                sys.exit(0)
            else:
                print(f"{self.RED}Please enter 'y' for yes, 'n' for no, or 'q' to quit.{self.END}")
    
    def simulate_delay(self, min_seconds=1, max_seconds=3):
        """Simulate processing delay"""
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)

    def simulate_realistic_processing(self, target_seconds: int):
        """Simulate realistic processing with natural delays and progress indicators"""
        # Break down the target time into smaller chunks with varying delays
        total_time = 0
        target_time = random.uniform(target_seconds - 5, target_seconds + 5)  # Add some variance

        # Initial processing delay
        initial_delay = random.uniform(2, 5)
        time.sleep(initial_delay)
        total_time += initial_delay

        # Show some processing steps with delays
        processing_steps = [
            "Initializing processing engine...",
            "Loading configuration parameters...",
            "Connecting to database systems...",
            "Retrieving relevant data...",
            "Applying business logic...",
            "Validating intermediate results...",
            "Performing final calculations...",
            "Generating output..."
        ]

        # Calculate remaining time for processing steps
        remaining_time = target_time - total_time - 2  # Leave 2 seconds for final delay
        step_time = remaining_time / len(processing_steps)

        for step in processing_steps:
            if total_time >= target_time - 2:
                break

            # Vary the delay for each step
            delay = random.uniform(step_time * 0.5, step_time * 1.5)

            # Occasionally show processing steps (not always)
            if random.random() < 0.3:  # 30% chance to show step
                print(f"{self.BLUE} {step}{self.END}")

            time.sleep(delay)
            total_time += delay

        # Final delay to reach target time
        if total_time < target_time:
            time.sleep(target_time - total_time)
    
    def run_email_monitoring(self) -> str:
        """Run email monitoring stage"""
        self.print_stage(0, "Email Monitoring")
        
        print(" Checking email for new purchase orders...")
        self.simulate_delay(1, 2)
        
        print(f"{self.BLUE} Found email from: <EMAIL>{self.END}")
        print(f"{self.BLUE} Subject: Purchase Order PO-VALID-2025-002{self.END}")
        print(f"{self.BLUE} Contains PO attachment: purchase_order_details.txt{self.END}")
        
        self.simulate_delay(1, 2)
        
        # Create PO file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        po_filename = f"PO_DEMO_{timestamp}.txt"

        # Ensure directory exists
        os.makedirs("data/incoming_pos", exist_ok=True)

        # Write PO content based on sample_po.txt
        po_path = Path("data/incoming_pos") / po_filename
        with open(po_path, 'w') as f:
            f.write(f"""=== EMAIL METADATA ===
Email ID: EMAIL-{timestamp}
Timestamp: {datetime.now().isoformat()}
Sender: <EMAIL>
Subject: Purchase Order PO-VALID-2025-002

=== EMAIL BODY ===
Please find attached our purchase order for immediate processing.

=== TEXT ATTACHMENTS ===
ATTACHMENT: purchase_order_details.txt

PURCHASE ORDER

PO Number: PO-VALID-2025-002
Date: July 22, 2025
Vendor: Test Supplier Inc.

BILL TO:
ABC Manufacturing Corp
123 Business Street
Anytown, CA 90210
Phone: (*************
Email: <EMAIL>

SHIP TO:
ABC Manufacturing Corp - Warehouse
456 Industrial Blvd
Anytown, CA 90211
Attention: Receiving Department

ORDER DETAILS:
Requested Delivery Date: August 10, 2025
Payment Terms: Net 30 Days
Shipping Method: Standard Ground

LINE ITEMS:
1. Item Code: WIDGET-A100
   Description: Premium Widget Assembly Type A
   Quantity: 25 units
   Unit Price: $125.00
   Line Total: $3,125.00

2. Item Code: BOLT-M8-50
   Description: M8 x 50mm Stainless Steel Bolts
   Quantity: 100 pieces
   Unit Price: $2.50
   Line Total: $250.00

PRICING SUMMARY:
Subtotal: $3,375.00
Tax (8.25%): $278.44
Shipping & Handling: $75.00
TOTAL AMOUNT: $3,728.44

Authorized By: Sarah Johnson
Title: Procurement Manager
""")
        
        self.print_success(f"PO extracted and saved: {po_filename}")
        return po_filename

    def run_po_parsing(self, po_filename: str) -> bool:
        """Run PO parsing stage"""
        stage_start_time = time.time()

        self.print_stage(1, "PO Parsing")

        print("This stage will extract structured data from the purchase order file.")
        print("The PO will be parsed to extract customer info, line items, pricing, etc.")
        print()

        print(" Parsing purchase order...")

        # Simulate realistic processing time (20-30 seconds)
        self.simulate_realistic_processing(25)

        # Show success message like in clogs.txt
        parsed_filename = f"PARSED_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        print(f"INFO:services.workflow_orchestrator:Successfully created parsed file: {parsed_filename}")

        # Create parsed data file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        actual_parsed_filename = f"parsed_PO_{timestamp}.json"

        os.makedirs("data/parsed_pos", exist_ok=True)
        parsed_path = Path("data/parsed_pos") / actual_parsed_filename

        with open(parsed_path, 'w') as f:
            json.dump(self.sample_po_data, f, indent=2)

        self.print_success("PO parsing completed successfully!")

        # Calculate actual execution time
        stage_end_time = time.time()
        execution_time = stage_end_time - stage_start_time
        self.stage_times.append(("PO Parsing", execution_time))

        # Display parsed data summary matching clogs.txt format
        print(f"\n{self.GREEN}{self.BOLD}Parsed PO Summary:{self.END}")
        print(f"{self.GREEN}{'─'*40}{self.END}")
        print(f"{self.CYAN}PO Number:{self.END} {self.sample_po_data['po_number']}")
        print(f"{self.CYAN}Customer:{self.END} {self.sample_po_data['customer']['name']}")
        print(f"{self.CYAN}Total Amount:{self.END} ${self.sample_po_data['pricing']['total']:,.2f}")
        print(f"{self.CYAN}Line Items:{self.END} {len(self.sample_po_data['line_items'])} items")
        print(f"{self.CYAN}Execution Time:{self.END} {execution_time:.2f} seconds")

        return True

    def run_order_validation(self) -> bool:
        """Run order validation stage"""
        stage_start_time = time.time()

        self.print_stage(2, "Order Validation")

        print("This stage will perform comprehensive validation of the parsed order.")
        print("Includes customer verification, product checks, and business rules.")
        print()

        print(" Running order validation...")

        # Show processing header like in clogs.txt
        print(f"{self.BLUE} Starting Comprehensive Order Validation...{self.END}")
        print("============================================================")
        print()

        # Simulate realistic processing time (20-30 seconds)
        self.simulate_realistic_processing(27)

        print("============================================================")
        print(f"{self.BLUE} Order Validation Complete!{self.END}")
        print("============================================================")

        # Display validation summary matching clogs.txt format
        print(f"\n{self.GREEN}{self.BOLD}Validation Summary:{self.END}")
        print(f"{self.GREEN}{'─'*40}{self.END}")
        print(f"{self.CYAN}Status:{self.END} {self.GREEN}SUCCESS{self.END}")
        print(f"{self.CYAN}Validation Result:{self.END} REVIEW")
        print()

        # Show detailed validation results like in clogs.txt
        print(f"{self.CYAN}Validation Details:{self.END}")
        print(f"   - Overall Validation Status: REVIEW REQUIRED")
        print(f"   - Available Credit: $37,500.00")
        print(f"   - Validation Message: Customer 'ABC Manufacturing Corp' validated successfully")
        print(f"   - Validation Message: All products validated successfully against catalog")
        print(f"   - Validation Message: Business rules validation failed due to missing required field")
        print(f"   - Delivery Address: 456 Industrial Blvd, Anytown, CA 90211 (Verified)")
        print(f"   - Issues Found: 0")
        print(f"   - Validation Message: Delivery information validated successfully")
        print(f"   - Validation Message: Order meets all compliance requirements")
        print(f"   - Validation Performed By: Senior Order Validation Specialist")
        print(f"   - Validation Timestamp: {datetime.now().isoformat()}")
        print(f"   - System: Enterprise Order Validation System v3.2")
        print()

        print(f"{self.CYAN}Risk Assessment:{self.END}")
        print(f"   - Overall Risk Score: 30 (LOW-MEDIUM)")

        # Calculate actual execution time
        stage_end_time = time.time()
        execution_time = stage_end_time - stage_start_time
        self.stage_times.append(("Order Validation", execution_time))
        print(f"{self.CYAN}Execution Time:{self.END} {execution_time:.2f} seconds")

        self.print_success("Order validation completed: SUCCESS")

        return True

    def run_credit_assessment(self) -> bool:
        """Run credit assessment stage"""
        stage_start_time = time.time()

        self.print_stage(3, "Credit Assessment")

        print("This stage will assess customer creditworthiness and payment risk.")
        print("Credit limits, payment history, and risk factors will be evaluated.")
        print()

        print(" Running credit assessment...")

        # Show processing header like in clogs.txt
        print(f"{self.BLUE} Starting Credit Management & Risk Assessment...{self.END}")
        print("============================================================")

        # Simulate realistic processing time (20-30 seconds)
        self.simulate_realistic_processing(22)

        # Show "Maximum iterations reached" message like in clogs.txt
        print(f"{self.BLUE} Maximum iterations reached. Requesting final answer.{self.END}")
        print("============================================================")
        print(f"{self.BLUE} Credit Assessment Complete!{self.END}")
        print("============================================================")

        # Display credit assessment summary matching clogs.txt format
        print(f"\n{self.GREEN}{self.BOLD}Credit Assessment Summary:{self.END}")
        print(f"{self.GREEN}{'─'*40}{self.END}")
        print(f"{self.CYAN}Status:{self.END} {self.GREEN}SUCCESS{self.END}")
        print()

        # Show detailed credit assessment results like in clogs.txt
        print(f"{self.CYAN}Credit Assessment Details:{self.END}")
        print(f"   - Validation Status: Approved with Conditions")
        print(f"   - Current Credit Limit: $50,000")
        print(f"   - Available Credit: $37,500")
        print(f"   - Risk Score: 20 (LOW Risk)")
        print(f"   - Comprehensive Risk Score: 72/100 (LOW-MEDIUM)")
        print(f"   **5. Credit Decision:**")
        print(f"   - Payment Terms: Net 30 (with 2% discount for payment within 10 days)")
        print(f"   - Credit Limit: Maintain at $50,000 (no increase recommended at this time)")

        # Calculate actual execution time
        stage_end_time = time.time()
        execution_time = stage_end_time - stage_start_time
        self.stage_times.append(("Credit Assessment", execution_time))
        print(f"{self.CYAN}Execution Time:{self.END} {execution_time:.2f} seconds")

        self.print_success("Credit assessment completed successfully!")

        return True

    def run_inventory_assessment(self) -> bool:
        """Run inventory assessment stage"""
        stage_start_time = time.time()

        self.print_stage(4, "Inventory Assessment")

        print("This stage will check product availability and plan fulfillment.")
        print("Stock levels, lead times, and fulfillment options will be evaluated.")
        print()

        print(" Running inventory assessment...")

        # Simulate realistic processing time (20-30 seconds)
        self.simulate_realistic_processing(28)

        # Show "Maximum iterations reached" message like in clogs.txt
        print(f"{self.BLUE} Maximum iterations reached. Requesting final answer.{self.END}")

        # Display inventory assessment summary matching clogs.txt format
        print(f"\n{self.GREEN}{self.BOLD}Inventory Assessment Summary:{self.END}")
        print(f"{self.GREEN}{'─'*40}{self.END}")
        print(f"{self.CYAN}Status:{self.END} {self.GREEN}SUCCESS{self.END}")
        print()

        print(f"{self.CYAN}Inventory Assessment Details:{self.END}")
        # Calculate actual execution time
        stage_end_time = time.time()
        execution_time = stage_end_time - stage_start_time
        self.stage_times.append(("Inventory Assessment", execution_time))
        print(f"{self.CYAN}Execution Time:{self.END} {execution_time:.2f} seconds")

        self.print_success("Inventory assessment completed successfully!")

        return True

    def run_pricing_assessment(self) -> bool:
        """Run pricing assessment stage"""
        stage_start_time = time.time()

        self.print_stage(5, "Pricing Assessment")

        print("This stage will calculate dynamic pricing and apply contract terms.")
        print("Base pricing, discounts, and contract conditions will be evaluated.")
        print()

        print(" Running pricing assessment...")

        # Simulate realistic processing time (20-30 seconds)
        self.simulate_realistic_processing(24)

        # Display pricing assessment summary matching clogs.txt format
        print(f"\n{self.GREEN}{self.BOLD}Pricing Assessment Summary:{self.END}")
        print(f"{self.GREEN}{'─'*40}{self.END}")
        print(f"{self.CYAN}Status:{self.END} {self.GREEN}SUCCESS{self.END}")
        print()

        print(f"{self.CYAN}Pricing Assessment Details:{self.END}")
        # Calculate actual execution time
        stage_end_time = time.time()
        execution_time = stage_end_time - stage_start_time
        self.stage_times.append(("Pricing Assessment", execution_time))
        print(f"{self.CYAN}Execution Time:{self.END} {execution_time:.2f} seconds")

        self.print_success("Pricing assessment completed successfully!")

        return True

    def run_fulfillment_coordination(self) -> bool:
        """Run fulfillment coordination stage"""
        stage_start_time = time.time()

        self.print_stage(6, "Fulfillment Coordination")

        print("This stage will coordinate warehouse operations and shipping.")
        print("Pick lists, shipping carriers, and delivery tracking will be set up.")
        print()

        print(" Running fulfillment coordination...")

        # Simulate realistic processing time (20-30 seconds)
        self.simulate_realistic_processing(29)

        # Show "Maximum iterations reached" message like in clogs.txt
        print(f"{self.BLUE} Maximum iterations reached. Requesting final answer.{self.END}")

        # Display fulfillment coordination summary matching clogs.txt format
        print(f"\n{self.GREEN}{self.BOLD}Fulfillment Coordination Summary:{self.END}")
        print(f"{self.GREEN}{'─'*40}{self.END}")
        print(f"{self.CYAN}Status:{self.END} {self.GREEN}SUCCESS{self.END}")
        print()

        print(f"{self.CYAN}Fulfillment Coordination Details:{self.END}")
        # Calculate actual execution time
        stage_end_time = time.time()
        execution_time = stage_end_time - stage_start_time
        self.stage_times.append(("Fulfillment Coordination", execution_time))
        print(f"{self.CYAN}Execution Time:{self.END} {execution_time:.2f} seconds")

        self.print_success("Fulfillment coordination completed successfully!")

        return True

    def run_invoice_generation(self) -> bool:
        """Run invoice generation stage"""
        stage_start_time = time.time()

        self.print_stage(7, "Invoice Generation & Email Distribution")

        print("This stage will generate a professional invoice and email it to the customer.")
        print("Payment terms, contact information, and delivery confirmation will be included.")
        print()

        print("� Running invoice generation...")

        # Show error messages like in clogs.txt (but continue processing)
        # print("ERROR:agents.tools.invoice_generation_tools: Error generating invoice: 'str' object has no attribute 'get'")
        # print("ERROR:agents.tools.invoice_generation_tools: Error generating invoice: 'str' object has no attribute 'get'")

        # Simulate realistic processing time (20-30 seconds)
        self.simulate_realistic_processing(26)

        # Show "Maximum iterations reached" message like in clogs.txt
        print(f"{self.BLUE} Maximum iterations reached. Requesting final answer.{self.END}")

        # Create invoice file
        os.makedirs("data/invoices", exist_ok=True)
        invoice_number = f"INV-{datetime.now().strftime('%Y%m%d')}-{random.randint(1000, 9999)}"
        invoice_filename = f"{invoice_number}.pdf"
        invoice_path = Path("data/invoices") / invoice_filename

        # Create a PDF placeholder
        final_total = 3456.78  # Discounted total from pricing stage
        with open(invoice_path, 'w') as f:
            f.write(f"PDF INVOICE - {invoice_number}\n")
            f.write(f"Generated: {datetime.now().isoformat()}\n")
            f.write(f"Customer: {self.sample_po_data['customer']['name']}\n")
            f.write(f"Total: ${final_total:.2f}\n")

        # Display invoice generation summary matching clogs.txt format
        print(f"\n{self.GREEN}{self.BOLD}Invoice Generation Summary:{self.END}")
        print(f"{self.GREEN}{'─'*40}{self.END}")
        print(f"{self.CYAN}Status:{self.END} {self.GREEN}SUCCESS{self.END}")
        print()

        print(f"{self.CYAN}Invoice Generation Details:{self.END}")
        # Calculate actual execution time
        stage_end_time = time.time()
        execution_time = stage_end_time - stage_start_time
        self.stage_times.append(("Invoice Generation", execution_time))
        print(f"{self.CYAN}Execution Time:{self.END} {execution_time:.2f} seconds")

        self.print_success("Invoice generation completed successfully!")

        return True

    def display_final_success(self):
        """Display final success message"""
        self.print_header("O2C WORKFLOW COMPLETED SUCCESSFULLY!")

        print(f"{self.GREEN}{self.BOLD}All 7 stages completed successfully!{self.END}")
        print(f"{self.GREEN}Complete Order-to-Cash workflow executed from PO to Invoice.{self.END}")

        # Show key metrics
        po_number = self.sample_po_data['po_number']
        customer = self.sample_po_data['customer']['name']
        total = 3456.78  # Final discounted total

        print(f"\n{self.CYAN}{self.BOLD}Order Summary:{self.END}")
        print(f"{self.CYAN}PO Number:{self.END} {po_number}")
        print(f"{self.CYAN}Customer:{self.END} {customer}")
        print(f"{self.CYAN}Original Value:{self.END} ${self.sample_po_data['pricing']['total']:,.2f}")
        print(f"{self.CYAN}Final Value:{self.END} ${total:,.2f}")
        print(f"{self.CYAN}Customer Savings:{self.END} ${self.sample_po_data['pricing']['total'] - total:,.2f}")
        print(f"{self.CYAN}Status:{self.END} {self.GREEN}INVOICE SENT{self.END}")

        print(f"\n{self.BLUE}{self.BOLD}Workflow Performance:{self.END}")
        # Calculate total processing time
        total_time = sum(time for _, time in self.stage_times)
        print(f" Total Processing Time: {total_time:.2f} seconds")
        print(f" All validations passed")
        print(f" Credit approved")
        print(f" Inventory allocated successfully")
        print(f" Pricing optimized")
        print(f" Fulfillment scheduled")
        print(f" Invoice generated and emailed")

        # Show individual stage times
        # print(f"\n{self.BLUE}{self.BOLD}Stage Execution Times:{self.END}")
        # for stage_name, stage_time in self.stage_times:
        #     print(f" {stage_name}: {stage_time:.2f} seconds")

        # print(f"\n{self.BLUE}{self.BOLD}Next Steps:{self.END}")
        # print(" Monitor delivery tracking")
        # print(" Follow up on payment in 25 days")
        # print(" Archive completed order")
        # print(" Update customer relationship data")

        # print(f"\n{self.BLUE}{self.BOLD}System Information:{self.END}")
        # print(f" Processing completed using advanced AI agents")
        # print(f" All data validated against master database")
        # print(f" Workflow optimized for efficiency and accuracy")

    def run_stage_by_stage_workflow(self):
        """Run workflow stage by stage with user approval for each"""

        # Show workflow stages
        print(f"\n{self.BLUE}{self.BOLD}Interactive Stage-by-Stage Workflow:{self.END}")
        print("This executes the complete O2C workflow with full control.")
        print("You will be asked to approve each stage before execution.")
        print()
        print("Stages:")
        print("1. Email Monitoring - Detect and process PO")
        print("2. PO Parsing - Extract structured data")
        print("3. Order Validation - Comprehensive validation")
        print("4. Credit Assessment - Customer credit check")
        print("5. Inventory Assessment - Stock availability")
        print("6. Pricing Assessment - Dynamic pricing")
        print("7. Fulfillment Coordination - Shipping setup")
        print("8. Invoice Generation - Create and email invoice")

        if not self.get_user_approval("Start stage-by-stage execution?"):
            return

        # Start tracking total workflow time
        self.total_start_time = time.time()

        try:
            # Stage 0: Email Monitoring
            if self.get_user_approval("Run Email Monitoring stage?"):
                po_filename = self.run_email_monitoring()

                # Stage 1: PO Parsing
                if self.get_user_approval("Run PO Parsing stage?"):
                    if self.run_po_parsing(po_filename):

                        # Stage 2: Order Validation
                        if self.get_user_approval("Run Order Validation stage?"):
                            if self.run_order_validation():

                                # Stage 3: Credit Assessment
                                if self.get_user_approval("Run Credit Assessment stage?"):
                                    if self.run_credit_assessment():

                                        # Stage 4: Inventory Assessment
                                        if self.get_user_approval("Run Inventory Assessment stage?"):
                                            if self.run_inventory_assessment():

                                                # Stage 5: Pricing Assessment
                                                if self.get_user_approval("Run Pricing Assessment stage?"):
                                                    if self.run_pricing_assessment():

                                                        # Stage 6: Fulfillment Coordination
                                                        if self.get_user_approval("Run Fulfillment Coordination stage?"):
                                                            if self.run_fulfillment_coordination():

                                                                # Stage 7: Invoice Generation
                                                                if self.get_user_approval("Run Invoice Generation stage?"):
                                                                    if self.run_invoice_generation():
                                                                        self.display_final_success()
                                                                    else:
                                                                        self.print_error("Workflow stopped at Stage 7")
                                                                else:
                                                                    self.print_warning("Stage 7 skipped by user")
                                                            else:
                                                                self.print_error("Workflow stopped at Stage 6")
                                                        else:
                                                            self.print_warning("Stage 6 skipped by user")
                                                    else:
                                                        self.print_error("Workflow stopped at Stage 5")
                                                else:
                                                    self.print_warning("Stage 5 skipped by user")
                                            else:
                                                self.print_error("Workflow stopped at Stage 4")
                                        else:
                                            self.print_warning("Stage 4 skipped by user")
                                    else:
                                        self.print_error("Workflow stopped at Stage 3")
                                else:
                                    self.print_warning("Stage 3 skipped by user")
                            else:
                                self.print_error("Workflow stopped at Stage 2")
                        else:
                            self.print_warning("Stage 2 skipped by user")
                    else:
                        self.print_error("Workflow stopped at Stage 1")
                else:
                    self.print_warning("Stage 1 skipped by user")
            else:
                self.print_warning("Stage 0 skipped by user")

        except Exception as e:
            self.print_error(f"Workflow execution failed: {str(e)}")

    def run_complete_workflow(self):
        """Run the complete O2C workflow with user interaction"""
        self.print_header("O2C INTERACTIVE WORKFLOW")

        # print("Welcome to the O2C Interactive CLI!")
        # print("This executes the complete Order-to-Cash workflow with full control.")
        # print("All processing uses advanced AI agents and real-time database validation.")
        # print("You will be asked to approve each stage before execution.")
        # print()
        # print(f"{self.YELLOW} Data Sources:{self.END}")
        # print(f" Sample PO: sample_po.txt")
        # print(f" Customer Data: ABC Manufacturing Corp (CUST-001)")
        # print(f" Products: WIDGET-A100, BOLT-M8-50")
        # print(f" Inventory: MAIN warehouse")
        # print(f" Business Rules: From SQLite database")

        if not self.get_user_approval("Start the O2C workflow?"):
            return

        # Run stage-by-stage workflow
        self.run_stage_by_stage_workflow()


def main():
    """Main CLI entry point"""
    cli = DemoO2CCLI()

    try:
        cli.run_complete_workflow()
    except KeyboardInterrupt:
        print(f"\n\n{cli.RED}Workflow interrupted by user (Ctrl+C).{cli.END}")
    except Exception as e:
        print(f"\n\n{cli.RED}Unexpected error: {str(e)}{cli.END}")


if __name__ == "__main__":
    main()
