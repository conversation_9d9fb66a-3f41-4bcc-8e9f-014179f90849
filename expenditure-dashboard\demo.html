<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dark Theme Demo - AI Expenditure Dashboard</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: #e2e8f0;
            margin: 0;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        
        .demo-card {
            background: rgba(45, 55, 72, 0.95);
            border-radius: 16px;
            padding: 3rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 2rem;
        }
        
        h1 {
            color: #f7fafc;
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        .subtitle {
            color: #a0aec0;
            font-size: 1.2rem;
            margin-bottom: 2rem;
        }
        
        .demo-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }
        
        .feature {
            background: rgba(26, 32, 44, 0.8);
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid #4a5568;
        }
        
        .feature h3 {
            color: #f7fafc;
            margin-bottom: 0.5rem;
        }
        
        .feature p {
            color: #a0aec0;
            font-size: 0.9rem;
        }
        
        .theme-info {
            background: rgba(26, 32, 44, 0.8);
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #4a5568;
            margin-top: 2rem;
        }
        
        .color-palette {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .color-swatch {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            border: 2px solid #4a5568;
        }
        
        .bg-primary { background: #667eea; }
        .bg-secondary { background: #764ba2; }
        .bg-success { background: #38a169; }
        .bg-warning { background: #ed8936; }
        .bg-dark { background: #2d3748; }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-card">
            <h1>🌙 Dark Theme Dashboard</h1>
            <p class="subtitle">Professional dark theme for AI expenditure analysis</p>
            
            <a href="index.html" class="demo-button">
                🚀 Launch Full Dashboard
            </a>
            
            <div class="features">
                <div class="feature">
                    <h3>🎨 Modern Design</h3>
                    <p>Sleek dark theme with professional gradients and smooth animations</p>
                </div>
                <div class="feature">
                    <h3>📊 Interactive Charts</h3>
                    <p>Chart.js visualizations optimized for dark backgrounds</p>
                </div>
                <div class="feature">
                    <h3>📱 Responsive</h3>
                    <p>Fully responsive design that works on all devices</p>
                </div>
                <div class="feature">
                    <h3>⚡ Performance</h3>
                    <p>Optimized for fast loading and smooth interactions</p>
                </div>
            </div>
        </div>
        
        <div class="theme-info">
            <h2 style="color: #f7fafc; margin-bottom: 1rem;">Dark Theme Features</h2>
            <p style="color: #a0aec0; margin-bottom: 1rem;">
                The dashboard now features a professional dark theme with carefully selected colors 
                for optimal readability and visual appeal. Perfect for presentations and extended viewing sessions.
            </p>
            
            <h3 style="color: #f7fafc; margin-bottom: 0.5rem;">Color Palette</h3>
            <div class="color-palette">
                <div class="color-swatch bg-primary" title="Primary: #667eea"></div>
                <div class="color-swatch bg-secondary" title="Secondary: #764ba2"></div>
                <div class="color-swatch bg-success" title="Success: #38a169"></div>
                <div class="color-swatch bg-warning" title="Warning: #ed8936"></div>
                <div class="color-swatch bg-dark" title="Dark: #2d3748"></div>
            </div>
            
            <div style="margin-top: 2rem;">
                <h3 style="color: #f7fafc; margin-bottom: 0.5rem;">Key Improvements</h3>
                <ul style="color: #a0aec0; text-align: left; max-width: 500px; margin: 0 auto;">
                    <li>Dark gradient backgrounds for reduced eye strain</li>
                    <li>High contrast text for excellent readability</li>
                    <li>Subtle borders and shadows for depth</li>
                    <li>Chart colors optimized for dark backgrounds</li>
                    <li>Professional appearance for business presentations</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
